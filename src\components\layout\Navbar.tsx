"use client";

import { useState } from "react";
import { FiChevronDown, FiGlobe } from "react-icons/fi";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";

interface NavLink {
  label: string;
  href: string;
  active?: boolean;
}

interface NavbarProps {
  language: string;
  onLanguageChange: (lang: string) => void;
  isMenuOpen?: boolean;
  setIsMenuOpen?: (isOpen: boolean) => void;
}

export const Navbar = ({
  language,
  onLanguageChange,
  isMenuOpen: propIsMenuOpen = false,
  setIsMenuOpen: propSetIsMenuOpen,
}: NavbarProps) => {
  // Use props if provided, otherwise use local state
  const [localIsMenuOpen, setLocalIsMenuOpen] = useState(false);
  const [isLanguageOpen, setIsLanguageOpen] = useState(false);
  const pathname = usePathname();

  // Determine whether to use props or local state
  const isMenuOpen = propSetIsMenuOpen !== undefined ? propIsMenuOpen : localIsMenuOpen;
  const setIsMenuOpen = propSetIsMenuOpen || setLocalIsMenuOpen;

  const navLinks: NavLink[] = [
    { label: "Menu", href: "/menu" },
    { label: "About", href: "/about" },
    { label: "Events", href: "/events" },
    { label: "Hours & Info", href: "/hours-info" },
  ];



  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const toggleLanguageDropdown = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsLanguageOpen(!isLanguageOpen);
  };

  const handleMobileMenuClose = () => {
    setIsMenuOpen(false);
  };

  const handleLanguageChange = (lang: string) => {
    onLanguageChange(lang);
    setIsLanguageOpen(false);
  };


  return (
    <nav className="w-full fixed top-0 left-0 z-50 bg-[#1B2951] py-3">
      <div className="container mx-auto px-6">
        <div className="flex items-center justify-between">
          {/* Logo - Left */}
          <div className="flex-shrink-0">
            <Link href="/" className="block">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-[#D4AF37] rounded-full flex items-center justify-center mr-2">
                  <span className="text-[#1B2951] font-bold text-sm">bl</span>
                </div>
                <span className="text-[#D4AF37] font-bold text-lg">BRASSERIE</span>
              </div>
            </Link>
          </div>

          {/* Center Navigation Links */}
          <div className="hidden lg:flex items-center space-x-8">
            {navLinks.map((link) => (
              <Link
                key={link.label}
                href={link.href}
                className={`relative text-white hover:text-[#D4AF37] transition-colors text-sm font-medium ${
                  pathname === link.href ? "text-[#D4AF37] font-semibold" : ""
                }`}
              >
                {link.label}
              </Link>
            ))}
          </div>

          {/* Right Section - Action Buttons */}
          <div className="flex items-center space-x-4">
            {/* Private Events Button */}
            <Link
              href="/private-events"
              className="hidden lg:inline-block border border-white text-white text-sm font-medium px-4 py-2 rounded transition-all hover:bg-white hover:text-[#1B2951]"
            >
              Private Events
            </Link>

            {/* Reserve Table Button */}
            <Link
              href="/reserve"
              className="hidden lg:inline-block bg-[#D4AF37] text-white text-sm font-medium px-6 py-2 rounded transition-all hover:bg-[#D4AF37]/90"
            >
              Reserve Table
            </Link>

            {/* Mobile menu button */}
            <button
              onClick={toggleMenu}
              className="lg:hidden text-white hover:text-[#D4AF37] transition-colors p-2 ml-2"
              aria-label="Menu"
            >
              {isMenuOpen ? (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              ) : (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                </svg>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black/80 z-40 pt-20 px-6"
          onClick={handleMobileMenuClose}
        >
          <div
            className="bg-[#1B2951] rounded-lg p-6 max-w-md mx-auto"
            onClick={(e) => e.stopPropagation()}
          >
            <nav className="flex flex-col space-y-4">
              {navLinks.map((link) => (
                <Link
                  key={link.href}
                  href={link.href}
                  onClick={() => setIsMenuOpen(false)}
                  className={`text-lg py-2 px-4 rounded transition-colors block ${
                    pathname === link.href
                      ? 'text-[#D4AF37] font-semibold bg-white/20'
                      : 'text-white/80 hover:bg-white/10 hover:text-white'
                  }`}
                >
                  {link.label}
                </Link>
              ))}
              <div className="pt-4 mt-4 border-t border-white/10 space-y-3">
                <Link
                  href="/private-events"
                  onClick={() => setIsMenuOpen(false)}
                  className="block border border-white text-white text-center py-2 px-4 rounded transition-all hover:bg-white hover:text-[#1B2951]"
                >
                  Private Events
                </Link>
                <Link
                  href="/reserve"
                  onClick={() => setIsMenuOpen(false)}
                  className="block bg-[#D4AF37] text-white text-center py-2 px-4 rounded transition-all hover:bg-[#D4AF37]/90"
                >
                  Reserve Table
                </Link>
              </div>
            </nav>
          </div>
        </div>
      )}
    </nav>
  );
};
