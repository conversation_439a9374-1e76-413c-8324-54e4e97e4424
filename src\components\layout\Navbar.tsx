"use client";

import { useState, useEffect, useCallback } from "react";
import { FiChevronDown, FiGlobe } from "react-icons/fi";
import Image from "next/image";
import Link from "next/link";

// Enhanced debounce implementation with cancel method
type DebouncedFunction<F extends (...args: unknown[]) => unknown> = {
  (...args: Parameters<F>): void;
  cancel: () => void;
};

const debounce = <F extends (...args: unknown[]) => unknown>(
  func: F,
  wait: number
): DebouncedFunction<F> => {
  let timeout: NodeJS.Timeout | null = null;
  
  const debounced = (...args: Parameters<F>) => {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => {
      func(...args);
      timeout = null;
    }, wait);
  };
  
  debounced.cancel = () => {
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
    }
  };
  
  return debounced;
};

interface NavLink {
  label: string;
  href: string;
  active?: boolean;
}

interface NavbarProps {
  language: string;
  onLanguageChange: (lang: string) => void;
  isMenuOpen?: boolean;
  setIsMenuOpen?: (isOpen: boolean) => void;
  // Search functionality is kept for future implementation
  // isSearchOpen?: boolean;
  // setIsSearchOpen?: (isOpen: boolean) => void;
}

export const Navbar = ({ 
  language, 
  onLanguageChange, 
  isMenuOpen: propIsMenuOpen = false, 
  setIsMenuOpen: propSetIsMenuOpen,
  // Search props are commented out until needed
  // isSearchOpen: _propIsSearchOpen = false, 
  // setIsSearchOpen: _propSetIsSearchOpen 
}: NavbarProps) => {
  // Use props if provided, otherwise use local state
  const [localIsMenuOpen, setLocalIsMenuOpen] = useState(false);
  const [isLanguageOpen, setIsLanguageOpen] = useState(false);
  
  // Determine whether to use props or local state
  const isMenuOpen = propSetIsMenuOpen !== undefined ? propIsMenuOpen : localIsMenuOpen;
  const setIsMenuOpen = propSetIsMenuOpen || setLocalIsMenuOpen;
  
  // Search state (kept for future implementation)
  // const [, setLocalIsSearchOpen] = useState(false);
  
  // Update local state when prop changes
  // useEffect(() => {
  //   if (_propSetIsSearchOpen !== undefined) {
  //     setLocalIsSearchOpen(_propIsSearchOpen);
  //   }
  // }, [_propIsSearchOpen, _propSetIsSearchOpen, setLocalIsSearchOpen]);
  const [activeSection, setActiveSection] = useState("#home");

  const navLinks: NavLink[] = [
    { label: "Home", href: "#home" },
    { label: "About us", href: "#about" },
    { label: "Foods", href: "#menu" },
    { label: "Location", href: "#location" },
  ];

  // Track scroll position to update active section
  useEffect(() => {
    const sections = document.querySelectorAll<HTMLElement>('section[id]');
    
    const handleScroll = debounce(() => {
      let currentSection = '';
      const scrollPosition = window.scrollY + 100; // Offset for header height
      
      // Find which section is currently in view
      sections.forEach((section: HTMLElement) => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.offsetHeight;
        
        if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
          currentSection = `#${section.id}`;
        }
      });
      
      if (currentSection && currentSection !== activeSection) {
        setActiveSection(currentSection);
      }
    }, 100);
    
    // Initial check
    handleScroll();
    
    // Add scroll event listener
    window.addEventListener('scroll', handleScroll);
    
    // Clean up
    return () => {
      const scrollHandler = handleScroll as DebouncedFunction<() => void>;
      if (scrollHandler.cancel) {
        scrollHandler.cancel();
      }
      window.removeEventListener('scroll', handleScroll);
    };
  }, [activeSection]);

  const scrollToSection = useCallback((e: React.MouseEvent<HTMLAnchorElement>, href: string) => {
    e.preventDefault();
    const element = document.querySelector(href);
    if (element) {
      // First update the active section to provide immediate feedback
      setActiveSection(href);
      // Then scroll to the section
      element.scrollIntoView({ behavior: 'smooth' });
      // Update URL without page reload
      window.history.pushState({}, '', href);
      // Close mobile menu if open
      if (isMenuOpen) {
        setIsMenuOpen(false);
      }
    }
  }, [isMenuOpen, setIsMenuOpen]);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const toggleLanguageDropdown = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsLanguageOpen(!isLanguageOpen);
  };

  const handleMobileMenuClose = () => {
    setIsMenuOpen(false);
  };

  const handleLanguageChange = (lang: string) => {
    onLanguageChange(lang);
    setIsLanguageOpen(false);
  };


  return (
    <nav className="w-full fixed top-0 left-0 z-50 bg-[rgba(30,25,25,0.7)] backdrop-blur-none py-2">
      <div className="container mx-auto px-6">
        <div className="flex items-center justify-between">
          {/* Left Navigation Links */}
          <div className="hidden lg:flex items-center space-x-6">
            {navLinks.map((link) => (
              <div key={link.label} className="relative group">
                <a
                  href={link.href}
                  onClick={(e) => scrollToSection(e, link.href)}
                  className={`relative text-[#F5F5F5] hover:text-white transition-colors text-sm font-medium px-2 py-1 ${
                    activeSection === link.href ? "font-semibold" : ""
                  }`}
                >
                  {link.label}
                  {activeSection === link.href && (
                    <div className="absolute left-1/2 -bottom-2 transform -translate-x-1/2 w-1.5 h-1.5 bg-white rounded-full"></div>
                  )}
                </a>
              </div>
            ))}
          </div>

          {/* Logo - Centered */}
          <div className="flex-shrink-0">
            <Link href="/" className="block text-center">
              <Image src="/logo.jpg" alt="Logo" width={80} height={80} />
            </Link>
          </div>

          {/* Right Section - Language and Contact */}
          <div className="flex items-center space-x-6">
            {/* Language Selector */}
            <div className="relative">
              <button
                onClick={toggleLanguageDropdown}
                className="flex items-center text-[#F5F5F5] hover:text-white transition-colors"
              >
                <FiGlobe className="mr-1" />
                <span className="text-sm">{language}</span>
                <FiChevronDown className="ml-1 text-xs" />
              </button>

              {/* Language Dropdown */}
              {isLanguageOpen && (
                <div className="absolute right-0 mt-2 w-24 bg-white rounded shadow-lg py-1 z-50">
                  <button
                    onClick={() => handleLanguageChange("EN")}
                    className="block w-full text-left px-4 py-2 text-sm text-gray-800 hover:bg-gray-100"
                  >
                    English
                  </button>
                  <button
                    onClick={() => handleLanguageChange("ID")}
                    className="block w-full text-left px-4 py-2 text-sm text-gray-800 hover:bg-gray-100"
                  >
                    Spanish
                  </button>
                </div>
              )}
            </div>

            {/* Contact Button */}
            <Link
              href="/contact"
              className="hidden lg:inline-block bg-[rgba(50,50,50,0.5)] border border-[rgba(255,255,255,0.2)] text-[#F5F5F5] text-xs font-medium uppercase tracking-wider px-6 py-3 rounded-full transition-all hover:bg-[rgba(70,70,70,0.6)] hover:shadow-lg"
            >
              CONTACT US
            </Link>

            {/* Mobile menu button */}
            <button
              onClick={toggleMenu}
              className="lg:hidden text-[#F5F5F5] hover:text-white transition-colors p-2 ml-2"
              aria-label="Menu"
            >
              {isMenuOpen ? (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              ) : (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                </svg>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black/80 z-40 pt-24 px-6"
          onClick={handleMobileMenuClose}
        >
          <div
            className="bg-[#1E1919] rounded-lg p-6 max-w-md mx-auto"
            onClick={(e) => e.stopPropagation()}
          >
            <nav className="flex flex-col space-y-4">
              {navLinks.map((link) => (
                <a
                  key={link.href}
                  href={link.href}
                  onClick={(e) => scrollToSection(e, link.href)}
                  className={`text-lg py-2 px-4 rounded transition-colors block ${
                    activeSection === link.href 
                      ? 'text-white font-semibold bg-white/20' 
                      : 'text-white/80 hover:bg-white/10 hover:text-white'
                  }`}
                >
                  {link.label}
                </a>
              ))}
              <div className="pt-4 mt-4 border-t border-white/10">
                <div className="flex items-center justify-between">
                  <button
                    onClick={toggleLanguageDropdown}
                    className="flex items-center text-white"
                  >
                    <FiGlobe className="mr-2" />
                    <span className="mr-1">{language}</span>
                    <FiChevronDown />
                  </button>
                  <Link
                    href="/contact"
                    className="bg-amber-500 hover:bg-amber-600 text-white px-6 py-2 rounded-full text-sm font-medium transition-colors"
                  >
                    CONTACT US
                  </Link>
                </div>
                {isLanguageOpen && (
                  <div className="mt-2 bg-white rounded shadow-lg py-1 z-50">
                    <button
                      onClick={() => handleLanguageChange("EN")}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-800 hover:bg-gray-100"
                    >
                      English
                    </button>
                    <button
                      onClick={() => handleLanguageChange("ID")}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-800 hover:bg-gray-100"
                    >
                      Indonesia
                    </button>
                  </div>
                )}
              </div>
            </nav>
          </div>
        </div>
      )}
    </nav>
  );
};
