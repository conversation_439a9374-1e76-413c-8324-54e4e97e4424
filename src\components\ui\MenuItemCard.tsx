'use client';

import Image from 'next/image';
import { motion } from 'framer-motion';
import { Plus } from 'lucide-react';

interface MenuItemCardProps {
  name: string;
  price: number;
  image: string;
  description: string;
}

export const MenuItemCard = ({ name, price, image, description }: MenuItemCardProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      viewport={{ once: true }}
      className="bg-[#2a2a2a] rounded-2xl p-4 group hover:bg-[#333333] transition-colors"
    >
      <div className="relative aspect-square rounded-xl overflow-hidden mb-4">          <Image
            src={image}
            alt={name}
            fill
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 33vw, 25vw"
            className="object-cover transition-transform duration-300 group-hover:scale-110"
            quality={85}
          />
        <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity" />
        <button className="absolute right-4 bottom-4 w-10 h-10 bg-[#FDB92A] rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity hover:bg-[#FDB92A]/90">
          <Plus className="w-5 h-5 text-[#191919]" />
        </button>
      </div>
      
      <h3 className="text-lg font-medium mb-2">{name}</h3>
      <p className="text-sm text-gray-400 mb-3 line-clamp-2">{description}</p>
      <p className="text-[#FDB92A] font-semibold">${price}</p>
    </motion.div>
  );
};
