'use client';

import { motion } from 'framer-motion';
import { MenuItemCard } from '../ui/MenuItemCard';
import { Button } from '../ui/Button';

const FEATURED_MENU = [
  {
    id: '1',
    name: 'Seafood Bowl',
    price: 32,
    image: '/menu/seafood-bowl.jpg',
    description: 'Fresh seafood served with mixed greens and our special sauce'
  },
  {
    id: '2',
    name: 'Spicy Ramen',
    price: 25,
    image: '/menu/spicy-ramen.jpg',
    description: 'Authentic Japanese ramen with a spicy kick'
  },
  {
    id: '3',
    name: 'Grilled Salmon',
    price: 28,
    image: '/menu/grilled-salmon.jpg',
    description: 'Norwegian salmon grilled to perfection'
  },
  {
    id: '4',
    name: 'Vegetable Salad',
    price: 18,
    image: '/menu/veggie-salad.jpg',
    description: 'Fresh seasonal vegetables with house dressing'
  },
  {
    id: '5',
    name: 'Beef Steak',
    price: 45,
    image: '/menu/beef-steak.jpg',
    description: 'Prime cut beef steak with roasted vegetables'
  },
  {
    id: '6',
    name: 'Pasta Carbonara',
    price: 22,
    image: '/menu/carbonara.jpg',
    description: 'Creamy pasta with bacon and parmesan'
  },
  {
    id: '7',
    name: 'Asian Duck',
    price: 38,
    image: '/menu/asian-duck.jpg',
    description: 'Crispy duck with Asian-inspired sauce'
  },
  {
    id: '8',
    name: 'Berry Dessert',
    price: 15,
    image: '/menu/berry-dessert.jpg',
    description: 'Mixed berries with vanilla cream'
  },
];

export const MenuShowcase = () => {
  return (
    <section className="py-20 px-4">
      <div className="container mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl md:text-4xl font-playfair font-bold mb-4">
            Choose Our Menu
          </h2>
          <p className="text-gray-400 max-w-2xl mx-auto">
            Explore our carefully curated menu featuring dishes that combine 
            exquisite flavors with nutritious ingredients
          </p>
        </motion.div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {FEATURED_MENU.map((item) => (
            <MenuItemCard key={item.id} {...item} />
          ))}
        </div>

        <div className="text-center">
          <Button variant="primary" size="lg">
            View All Menu
          </Button>
        </div>
      </div>
    </section>
  );
};
