import { MenuItem, ServiceItem, TestimonialItem, Recipe } from '../lib/types';

export const MENU_ITEMS: MenuItem[] = [
  {
    id: 1,
    name: "Fried Rice With Meat",
    price: 23,
    image: "/menu/fried-rice.jpg",
    description: "Delicious fried rice with tender meat and fresh vegetables"
  },
  {
    id: 2,
    name: "Seafood Bowl",
    price: 32,
    image: "/menu/seafood-bowl.jpg",
    description: "Fresh seafood served with mixed greens"
  },
  // Add more menu items here
];

export const SERVICES: ServiceItem[] = [
  {
    id: 1,
    title: "Online Order",
    description: "Easy and convenient online ordering system",
    icon: "shopping-cart"
  },
  {
    id: 2,
    title: "24/7 Service",
    description: "Available round the clock for your convenience",
    icon: "clock"
  },
  // Add more services here
];

export const FEATURED_RECIPE: Recipe = {
  title: "Whole Wheat Bread With Avocado Sauce",
  ingredients: [
    "2 slices whole wheat bread",
    "1 ripe avocado",
    "Fresh herbs and spices",
    "Olive oil",
    "Salt and pepper to taste"
  ],
  image: "/recipes/avocado-toast.jpg"
};

export const TESTIMONIALS: TestimonialItem[] = [
  {
    id: 1,
    name: "<PERSON>",
    image: "/testimonials/user1.jpg",
    comment: "The best food experience in town! Every dish is a masterpiece.",
    rating: 5
  },
  // Add more testimonials here
];

export const NAV_LINKS = [
  { href: "/", label: "Home" },
  { href: "/menu", label: "Menu" },
  { href: "/about", label: "About" },
  { href: "/contact", label: "Contact" }
];
