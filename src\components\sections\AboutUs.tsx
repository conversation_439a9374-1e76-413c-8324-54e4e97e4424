"use client";

import { motion } from "framer-motion";
import Image from "next/image";
import { Button } from "../ui/Button";

export const AboutUs = () => {
  return (
    <section className="py-20 px-4 bg-[#191919]">
      <div className="container mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
          {/* Image Gallery Section */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="relative"
          >
            {/* Main Restaurant Interior Image */}
            <div className="relative">
              <div className="border-4 border-[#FDB92A] p-4 bg-white/5 backdrop-blur-sm">
                <div className="relative aspect-[4/3] overflow-hidden">
                  <Image
                    src="/restaurant-interior.jpg"
                    alt="Restaurant Interior"
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 40vw"
                    className="object-cover"
                    priority
                  />
                </div>
              </div>
            </div>

            {/* Overlapping Food Image */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="absolute -bottom-8 -right-8 md:-bottom-12 md:-right-12"
            >
              <div className="border-4 border-[#FDB92A] p-3 bg-white/10 backdrop-blur-sm">
                <div className="relative w-32 h-32 md:w-40 md:h-40 overflow-hidden">
                  <Image
                    src="/food-background.jpg"
                    alt="Delicious Food"
                    fill
                    sizes="(max-width: 768px) 128px, 160px"
                    className="object-cover"
                  />
                </div>
              </div>
            </motion.div>

            {/* Small Accent Image */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
              className="absolute -top-6 -right-6 md:-top-8 md:-right-8"
            >
              <div className="border-2 border-[#FDB92A] p-2 bg-white/10 backdrop-blur-sm">
                <div className="relative w-20 h-20 md:w-24 md:h-24 overflow-hidden">
                  <Image
                    src="/tables-bg.jpg"
                    alt="Restaurant Ambiance"
                    fill
                    sizes="(max-width: 768px) 80px, 96px"
                    className="object-cover"
                  />
                </div>
              </div>
            </motion.div>
          </motion.div>

          {/* Content Section */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            {/* About Us Header */}
            <motion.p
              initial={{ opacity: 0, y: 10 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true }}
              className="text-[#FDB92A] font-medium text-lg tracking-wide"
            >
              About Us
            </motion.p>

            {/* Main Heading */}
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
              className="text-3xl md:text-4xl lg:text-5xl font-playfair font-bold text-white leading-tight"
            >
              About the Barnea Group
            </motion.h2>

            {/* Description Paragraphs */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              viewport={{ once: true }}
              className="space-y-4"
            >
              <p className="text-gray-300 text-base md:text-lg leading-relaxed">
                With a strong focus on innovation, quality, and client satisfaction, we work 
                tirelessly to meet and exceed expectations. Learn more about how our 
                expertise can help you achieve your goals.
              </p>
              
              <p className="text-gray-300 text-base md:text-lg leading-relaxed">
                We specialize in delivering tailored solutions that drive success and create 
                lasting value for our clients. Discover how our expertise, dedication, and 
                collaborative approach can help you achieve your goals.
              </p>
            </motion.div>

            {/* Read More Button */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              viewport={{ once: true }}
              className="pt-4"
            >
              <Button
                variant="primary"
                size="lg"
                className="bg-[#FDB92A] text-[#191919] hover:bg-[#FDB92A]/90 font-semibold tracking-wide px-8 py-4"
              >
                READ MORE
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};
