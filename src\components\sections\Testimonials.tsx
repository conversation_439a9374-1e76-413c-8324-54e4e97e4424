'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';

const testimonials = [
  {
    id: '1',
    name: '<PERSON>',
    image: '/testimonials/person1.jpg',
    review: 'You Won\'t Find A Restaurant That Served Such Delicious Food. Their Menu Is Very Diverse And The Service Is Beyond Excellent.',
    rating: 5
  },
  {
    id: '2',
    name: '<PERSON>',
    image: '/testimonials/person2.jpg',
    review: 'The atmosphere and food here are simply amazing. Every dish is prepared with such attention to detail.',
    rating: 5
  }
];

export const Testimonials = () => {
  return (
    <section className="py-20 px-4 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute top-40 -left-20 w-40 h-40 bg-[#FDB92A]/5 rounded-full blur-3xl" />
      <div className="absolute bottom-20 right-0 w-60 h-60 bg-[#FDB92A]/5 rounded-full blur-3xl" />

      <div className="container mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl md:text-4xl font-playfair font-bold mb-4">
            What Our Customers Say
          </h2>
          <p className="text-gray-400 max-w-2xl mx-auto">
            Don&apos;t just take our word for it. Here&apos;s what our valued customers
            have to say about their dining experience with us.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={testimonial.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.2 }}
              viewport={{ once: true }}
              className="bg-dark-100 rounded-2xl p-6 relative"
            >
              <div className="flex items-start gap-4">
                <div className="relative w-12 h-12 rounded-full overflow-hidden flex-shrink-0">                  <Image
                    src={testimonial.image}
                    alt={testimonial.name}
                    fill
                    sizes="48px"
                    className="object-cover"
                    quality={90}
                  />
                </div>
                <div>
                  <h3 className="font-medium mb-1">{testimonial.name}</h3>
                  <div className="flex items-center gap-1 mb-3">
                    {'★'.repeat(testimonial.rating)}
                    {'☆'.repeat(5 - testimonial.rating)}
                  </div>
                  <p className="text-gray-400">{testimonial.review}</p>
                </div>
              </div>
              <div className="absolute -top-3 -right-3 text-6xl text-[#FDB92A]/10 font-serif">
                &quot;
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};
