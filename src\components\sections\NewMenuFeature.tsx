'use client';

import Image from 'next/image';
import { motion } from 'framer-motion';
import { Button } from '../ui/Button';

interface NewMenuFeatureProps {
  imagePosition?: 'left' | 'right';
}

export const NewMenuFeature = ({ imagePosition = 'left' }: NewMenuFeatureProps) => {
  return (
    <section className="py-20 px-4">
      <div className="container mx-auto">
        <div className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${
          imagePosition === 'right' ? 'lg:flex-row-reverse' : ''
        }`}>
          {/* Image Column */}
          <motion.div
            initial={{ opacity: 0, x: imagePosition === 'left' ? -20 : 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="relative aspect-square rounded-full overflow-hidden"
          >            <Image
              src="/menu/fried-rice.jpg"
              alt="Fried Rice With Meat"
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              className="object-cover"
              priority
            />
          </motion.div>

          {/* Content Column */}
          <motion.div
            initial={{ opacity: 0, x: imagePosition === 'left' ? 20 : -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className={`flex flex-col ${
              imagePosition === 'right' ? 'lg:items-end lg:text-right' : ''
            }`}
          >
            <h2 className="text-3xl md:text-4xl font-playfair font-bold mb-4">
              Fried Rice With Meat
              <br />
              <span className="text-[#FDB92A]">and Vegetables</span>
            </h2>
            <p className="text-gray-400 mb-6 max-w-lg">
              Savor our signature fried rice, perfectly seasoned and loaded with 
              tender meat and fresh vegetables. A harmonious blend of flavors 
              that will tantalize your taste buds.
            </p>
            <div className="flex items-center gap-6 mb-8">
              <span className="text-2xl font-bold text-[#FDB92A]">$23.00</span>
              <div className="h-8 w-px bg-gray-700" />
              <div className="flex items-center gap-1">
                <span className="text-yellow-400">★★★★★</span>
                <span className="text-gray-400">(4.8)</span>
              </div>
            </div>
            <Button variant="primary" size="lg">
              View Menu
            </Button>
          </motion.div>
        </div>
      </div>
    </section>
  );
};
